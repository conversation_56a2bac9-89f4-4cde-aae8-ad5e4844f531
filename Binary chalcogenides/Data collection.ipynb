import pandas as pd
import numpy as np
import ast
import torch
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Dropout
from torch_geometric.nn import GINConv, global_mean_pool, global_add_pool
from torch_geometric.data import Data, DataLoader
from torch_geometric.loader import DataLoader
import matplotlib.pyplot as plt
import networkx as nx
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Set random seeds for reproducibility
torch.manual_seed(11)
torch.cuda.manual_seed(0)
torch.cuda.manual_seed_all(0)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

# Load the dataset
data = pd.read_csv('/home/<USER>/Coding/Binary chalcogenides/Graph structure dataset for dichalcogenides (2).csv')
print(f"Dataset shape: {data.shape}")
print(f"Columns: {data.columns.tolist()}")
data.head()

# Extract unique elements from the dataset
all_elements = []
for d in data['Node Elements']:
    d_dict = dict(ast.literal_eval(d))
    all_elements.extend(d_dict.keys())

unique_elements = set(all_elements)
print(f"Unique elements: {unique_elements}")
print(f"Number of unique elements: {len(unique_elements)}")

# Extract A and B elements
A = []
B = []

for d in data['Node Elements']:
    d_dict = dict(ast.literal_eval(d))
    keys = list(d_dict.keys())
    A.append(keys[0])
    B.append(keys[1])

print(f"A elements: {set(A)}")
print(f"B elements: {set(B)}")

# Function to get element properties
def get_element_properties(element_symbol):
    from pymatgen.core import Element
    try:
        element = Element(element_symbol)
        return {
            'Element': element_symbol,
            'atomic_number': element.Z,
            'atomic_mass': element.atomic_mass,
            'atomic_radius': element.atomic_radius or 0,
            'electronegativity': element.X or 0,
            'ionization_energy': element.ionization_energy or 0,
            'electron_affinity': element.electron_affinity or 0,
            'melting_point': element.melting_point or 0
        }
    except:
        return {
            'Element': element_symbol,
            'atomic_number': 0,
            'atomic_mass': 0,
            'atomic_radius': 0,
            'electronegativity': 0,
            'ionization_energy': 0,
            'electron_affinity': 0,
            'melting_point': 0
        }

# Create element properties dataset
elements_list = []
for element in unique_elements:
    elements_list.append(get_element_properties(element))

elements_data = pd.DataFrame(elements_list).fillna(0)
print("Element properties dataset:")
elements_data

# Function to return element row
def return_elem_row(name):
    row = elements_data[elements_data['Element'] == name].select_dtypes(np.number).values.flatten().tolist()
    return row

# Create graph node dataset
Graph_node_dataset = []
for row in data['Node Elements']:
    material = []
    row = dict(ast.literal_eval(row))
    for elem, value in row.items():
        element_info = return_elem_row(elem)
        for val in value:
            val = list(val)
            val.extend(element_info)  # append element_info values to val
            material.append(val)
    Graph_node_dataset.append(material)

print(f"Number of graphs: {len(Graph_node_dataset)}")
print(f"Node features dimension: {len(Graph_node_dataset[0][0])}")

# Function to convert adjacency matrix to edge index
def adj_to_edge_index(adj):
    edge_index = []
    for i in range(len(adj)):
        for j in range(len(adj[i])):
            if adj[i, j] == 1.0:
                edge_index.append([i, j])
    return edge_index

# Create edge index dataset
Edge_index_dataset = []
for row in data['Adjacency Matrix']:
    row_list = ast.literal_eval(row)  # Convert string to list
    row_array = np.array(row_list)    # Convert list to np.array
    edge_index = adj_to_edge_index(row_array)
    Edge_index_dataset.append(edge_index)

print(f"Number of edge indices: {len(Edge_index_dataset)}")

# Extract target values (bandgap)
y = data['Bandgap (eV)'].values.tolist()
print(f"Target values (bandgap) - Min: {min(y):.3f}, Max: {max(y):.3f}, Mean: {np.mean(y):.3f}")

# Plot bandgap distribution
plt.figure(figsize=(10, 6))
plt.hist(y, bins=30, alpha=0.7, edgecolor='black')
plt.xlabel('Bandgap (eV)')
plt.ylabel('Frequency')
plt.title('Distribution of Bandgap Values')
plt.grid(True, alpha=0.3)
plt.show()

# Function to create graph data
def Create_graph_data(node, edge_index, y):
    X = torch.tensor(node)
    edge_index = torch.tensor(edge_index).T
    y = torch.tensor(y)
    return [X, edge_index, y]

# Create graphs
Graphs = []
for i in range(len(y)):
    graph = Create_graph_data(Graph_node_dataset[i], Edge_index_dataset[i], y[i])
    Graphs.append(graph)

print(f"Number of graphs created: {len(Graphs)}")
print(f"First graph - Nodes: {Graphs[0][0].shape}, Edges: {Graphs[0][1].shape}, Target: {Graphs[0][2]}")

# Visualize some graphs
fig, axes = plt.subplots(2, 5, figsize=(20, 8))
axes = axes.flatten()

for i in range(10):
    G = nx.Graph()
    # Add all nodes explicitly to ensure isolated nodes are included
    num_nodes = len(Graph_node_dataset[i])
    G.add_nodes_from(range(num_nodes))
    # Add edges
    for edge in Edge_index_dataset[i]:
        G.add_edge(edge[0], edge[1])
    pos = nx.spring_layout(G)
    ax = axes[i]
    nx.draw(G, pos, with_labels=True, ax=ax, node_color='lightblue', 
            node_size=300, font_size=8)
    ax.set_title(f'Graph {i} (Bandgap: {y[i]:.2f} eV)')

plt.tight_layout()
plt.show()



# Define the GIN model
class GIN(torch.nn.Module):
    """Graph Isomorphism Network (GIN) for regression"""
    def __init__(self, dim_h):
        super(GIN, self).__init__()
        self.conv1 = GINConv(
            Sequential(Linear(8, dim_h),  # 8 is the number of node features
                       BatchNorm1d(dim_h), ReLU(),
                       Linear(dim_h, dim_h), ReLU()))
        self.conv2 = GINConv(
            Sequential(Linear(dim_h, dim_h), BatchNorm1d(dim_h), ReLU(),
                       Linear(dim_h, dim_h), ReLU()))
        self.conv3 = GINConv(
            Sequential(Linear(dim_h, dim_h), BatchNorm1d(dim_h), ReLU(),
                       Linear(dim_h, dim_h), ReLU()))
        self.lin1 = Linear(dim_h*3, dim_h*3)
        self.lin2 = Linear(dim_h*3, 1)  # Output 1 value for regression

    def forward(self, x, edge_index, batch):
        # Node embeddings from each GIN layer
        h1 = self.conv1(x, edge_index)
        h2 = self.conv2(h1, edge_index)
        h3 = self.conv3(h2, edge_index)

        # Graph-level readout (concatenate all layer outputs)
        h1_g = global_add_pool(h1, batch)
        h2_g = global_add_pool(h2, batch)
        h3_g = global_add_pool(h3, batch)
        h = torch.cat([h1_g, h2_g, h3_g], dim=1)

        # Final prediction layers
        h = F.dropout(h, p=0.5, training=self.training)
        h = self.lin1(h)
        h = F.relu(h)
        h = F.dropout(h, p=0.5, training=self.training)
        h = self.lin2(h)
        
        return h.squeeze()

print("GIN model defined successfully!")

# Training function
def train(model, loader):
    model.train()
    criterion = torch.nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    
    total_loss = 0
    for data in loader:
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(loader)

# Test function
def test(model, loader):
    model.eval()
    total_loss = 0
    predictions = []
    targets = []
    criterion = torch.nn.MSELoss()
    
    with torch.no_grad():
        for data in loader:
            out = model(data.x, data.edge_index, data.batch)
            loss = criterion(out, data.y)
            total_loss += loss.item()
            predictions.extend(out.cpu().numpy())
            targets.extend(data.y.cpu().numpy())
    
    return total_loss / len(loader), np.array(predictions), np.array(targets)

print("Training and testing functions defined!")

# Prepare PyG Data objects from Graphs
pyg_graphs = []
for graph in Graphs:
    X, edge_index, y_val = graph
    data = Data(x=X.float(), edge_index=edge_index.long(), y=y_val.view(1).float())
    pyg_graphs.append(data)

print(f"Created {len(pyg_graphs)} PyG graph objects")
print(f"First graph: {pyg_graphs[0]}")

pyg_graphs[0].is_cuda

# Split dataset into train and test
split_idx = int(0.8 * len(pyg_graphs))
train_graphs = pyg_graphs[:split_idx]
test_graphs = pyg_graphs[split_idx:]

print(f"Training graphs: {len(train_graphs)}")
print(f"Test graphs: {len(test_graphs)}")

# Create data loaders
train_loader = DataLoader(train_graphs, batch_size=32, shuffle=True)
test_loader = DataLoader(test_graphs, batch_size=32, shuffle=False)

print(f"Training batches: {len(train_loader)}")
print(f"Test batches: {len(test_loader)}")